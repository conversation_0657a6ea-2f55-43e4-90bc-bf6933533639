# 新功能快速使用指南

## 🚀 快速开始

### 1. 加载EA
- 在MT5中加载 `TrendBreakoutReversal_EA copy.mq5`
- 确保允许自动交易和DLL导入

### 2. 基本设置

#### 默认配置（推荐新手）
```
Enable_Breakout_Strategy = true
Enable_Long_Breakout_Strategy = true
Enable_Short_Breakout_Strategy = true
Enable_Candle_Color_Filter = false
```
**效果**：双向交易，无K线颜色过滤

#### 启用K线颜色过滤
```
Enable_Candle_Color_Filter = true
Candle_Filter_Timeframe = PERIOD_H4
```
**效果**：只有4小时线为阳线时才做多，为阴线时才做空

### 3. 常用场景配置

#### 🔵 只做多头策略
```
Enable_Long_Breakout_Strategy = true
Enable_Short_Breakout_Strategy = false
```

#### 🔴 只做空头策略
```
Enable_Long_Breakout_Strategy = false
Enable_Short_Breakout_Strategy = true
```

#### ⚡ 强过滤模式
```
Enable_MA_Filter = true
Enable_Candle_Color_Filter = true
Candle_Filter_Timeframe = PERIOD_H4
```

#### 🛑 暂停所有策略
```
Enable_Breakout_Strategy = false
```

## 📊 仪表盘说明

### 第一列（多头监控）
- **上升趋势**：EMA趋势判断
- **前高价格**：突破参考价位
- **价格突破**：是否突破前高
- **成交量比**：当前成交量与均线比值
- **多头信号**：综合突破信号
- **突破策略**：总开关状态
- **多头策略**：多头开关状态 ⭐新增
- **K线过滤**：K线颜色过滤状态 ⭐新增
- **多头K线**：当前K线是否为阳线 ⭐新增

### 第二列（空头监控）
- **下降趋势**：EMA趋势判断
- **前低价格**：突破参考价位
- **价格跌破**：是否跌破前低
- **成交量比**：当前成交量与均线比值
- **空头信号**：综合突破信号
- **突破策略**：总开关状态
- **空头策略**：空头开关状态 ⭐新增
- **K线过滤**：K线颜色过滤状态 ⭐新增
- **空头K线**：当前K线是否为阴线 ⭐新增

### 第三列（仓位信息）
- **总仓位数**：当前持仓数量
- **总手数**：当前总手数
- **浮动盈亏**：当前浮动损益
- **账户余额**：账户余额
- **账户净值**：账户净值
- **下单风险**：下一单风险金额
- **风险比例**：风险百分比
- **策略状态**：当前策略状态
- **保本状态**：保本功能状态

## 🎯 实用技巧

### 市场适应策略
1. **牛市**：只开启多头策略
2. **熊市**：只开启空头策略
3. **震荡市**：启用K线颜色过滤
4. **重要数据前**：暂停所有策略

### 风险控制
1. **新手**：先用一个方向测试
2. **保守**：启用所有过滤器
3. **激进**：关闭过滤器，双向交易
4. **紧急**：快速关闭总开关

### 参数优化建议
1. **K线时间框架**：
   - H1：适合短期交易
   - H4：适合中期交易（推荐）
   - D1：适合长期交易

2. **策略组合**：
   - 新手：单向 + K线过滤
   - 进阶：双向 + 均线过滤
   - 专业：双向 + 全过滤

## ⚠️ 注意事项

1. **参数修改**：实时生效，无需重启EA
2. **K线监控**：监控的是当前正在形成的K线
3. **十字星**：开盘价=收盘价时会被过滤
4. **总开关**：优先级最高，关闭后子开关无效
5. **持仓管理**：关闭策略不影响已有持仓

## 🔧 故障排除

### 常见问题
1. **策略不执行**：检查总开关和子开关
2. **K线过滤不生效**：确认Enable_Candle_Color_Filter=true
3. **仪表盘不显示**：检查图表权限设置
4. **调试信息过多**：设置Debug_Mode=false

### 检查清单
- [ ] 总开关已启用
- [ ] 对应方向的子开关已启用
- [ ] K线过滤设置正确
- [ ] 时间过滤允许当前时间
- [ ] 账户有足够资金
- [ ] 网络连接正常

## 📞 技术支持

如遇问题，请检查：
1. 仪表盘显示的各项状态
2. 调试模式的输出信息
3. MT5的专家日志
4. 参数设置是否正确

记住：所有功能都是为了提高交易质量和风险控制，根据自己的交易风格灵活配置！
