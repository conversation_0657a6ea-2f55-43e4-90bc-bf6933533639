# 策略开关功能说明

## 功能概述

新增了分别控制多头突破策略和空头突破策略的独立开关，实现更精细的策略控制。

## 新增参数

```mql5
//--- 策略开关参数 ---
input group "========== 策略开关 =========="
input bool   Enable_Breakout_Strategy = true;        // 启用突破策略（总开关）
input bool   Enable_Long_Breakout_Strategy = true;   // 启用多头突破策略
input bool   Enable_Short_Breakout_Strategy = true;  // 启用空头突破策略
```

## 参数说明

### 1. Enable_Breakout_Strategy（总开关）
- **作用**：控制整个突破策略系统的启用/禁用
- **默认值**：true（启用）
- **说明**：这是主开关，如果禁用，则无论子开关如何设置，都不会执行任何突破策略

### 2. Enable_Long_Breakout_Strategy（多头策略开关）
- **作用**：单独控制多头突破策略的启用/禁用
- **默认值**：true（启用）
- **说明**：只影响多头突破策略，不影响空头策略

### 3. Enable_Short_Breakout_Strategy（空头策略开关）
- **作用**：单独控制空头突破策略的启用/禁用
- **默认值**：true（启用）
- **说明**：只影响空头突破策略，不影响多头策略

## 逻辑关系

### 策略执行条件
```
多头策略执行条件 = 总开关 && 多头开关 && 多头突破信号 && 其他过滤条件
空头策略执行条件 = 总开关 && 空头开关 && 空头突破信号 && 其他过滤条件
```

### 各种组合效果

| 总开关 | 多头开关 | 空头开关 | 执行效果 |
|--------|----------|----------|----------|
| ✓ | ✓ | ✓ | 多头和空头策略都执行 |
| ✓ | ✓ | ✗ | 只执行多头策略 |
| ✓ | ✗ | ✓ | 只执行空头策略 |
| ✓ | ✗ | ✗ | 不执行任何策略 |
| ✗ | 任意 | 任意 | 不执行任何策略 |

## 使用场景

### 1. 单向交易
**只做多头**：
```
Enable_Breakout_Strategy = true
Enable_Long_Breakout_Strategy = true
Enable_Short_Breakout_Strategy = false
```

**只做空头**：
```
Enable_Breakout_Strategy = true
Enable_Long_Breakout_Strategy = false
Enable_Short_Breakout_Strategy = true
```

### 2. 市场条件适应
**牛市环境**（偏向多头）：
```
Enable_Long_Breakout_Strategy = true
Enable_Short_Breakout_Strategy = false
```

**熊市环境**（偏向空头）：
```
Enable_Long_Breakout_Strategy = false
Enable_Short_Breakout_Strategy = true
```

### 3. 风险控制
**暂停所有策略**：
```
Enable_Breakout_Strategy = false
```

**暂停特定方向**：
```
Enable_Long_Breakout_Strategy = false  // 暂停多头
// 或
Enable_Short_Breakout_Strategy = false  // 暂停空头
```

## 仪表盘显示

### 第一列（多头监控）新增
- **多头策略**：显示多头策略的启用状态
  - 绿色 ✓：启用
  - 红色 ✗：禁用

### 第二列（空头监控）新增
- **空头策略**：显示空头策略的启用状态
  - 绿色 ✓：启用
  - 红色 ✗：禁用

### 显示逻辑
```
多头策略状态 = 总开关 && 多头开关
空头策略状态 = 总开关 && 空头开关
```

## 调试信息

启用调试模式时，会输出：
```
策略开关 - 突破策略: 启用/禁用
策略开关 - 多头突破: 启用/禁用 空头突破: 启用/禁用
```

## 实际应用建议

### 1. 新手使用
- 建议先启用一个方向的策略进行测试
- 熟悉后再开启双向交易

### 2. 市场分析
- 根据技术分析判断市场趋势方向
- 在明确的趋势中只开启对应方向的策略

### 3. 风险管理
- 在重要经济数据发布前暂停策略
- 在市场波动剧烈时可以暂停某个方向的策略

### 4. 回测优化
- 分别测试多头和空头策略的表现
- 找出最适合的策略组合

## 技术实现

### 状态监控
```mql5
struct BreakoutMonitor {
    // ... 其他字段 ...
    bool long_strategy_enabled;   // 多头策略是否启用
    bool short_strategy_enabled;  // 空头策略是否启用
};
```

### 实时更新
```mql5
// 在每个tick中更新策略状态
breakout_monitor.long_strategy_enabled = Enable_Breakout_Strategy && Enable_Long_Breakout_Strategy;
breakout_monitor.short_strategy_enabled = Enable_Breakout_Strategy && Enable_Short_Breakout_Strategy;
```

### 交易执行检查
```mql5
// 多头策略执行检查
if(breakout_monitor.long_strategy_enabled && breakout_monitor.long_breakout_signal && can_open_long) {
    // 执行多头开仓逻辑
}

// 空头策略执行检查
if(breakout_monitor.short_strategy_enabled && breakout_monitor.short_breakout_signal && can_open_short) {
    // 执行空头开仓逻辑
}
```

## 注意事项

1. **总开关优先级最高**：如果总开关关闭，子开关无效
2. **实时生效**：参数修改后立即生效，无需重启EA
3. **持仓管理**：关闭策略不会影响已有持仓的管理
4. **统计独立**：多头和空头的交易统计仍然分别计算

这个功能让您可以根据市场情况和个人策略灵活控制交易方向，提高策略的适应性和风险控制能力。
